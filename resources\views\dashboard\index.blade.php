@extends('layouts.app')

@section('title', 'Dashboard - PawPortal')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </h1>
            <div class="text-muted">
                Welcome back, {{ $user->name }}!
            </div>
        </div>
    </div>
</div>

<!-- User Info Card -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user"></i> Your Profile
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Name:</strong> {{ $user->name }}</p>
                        <p><strong>Email:</strong> {{ $user->email }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Phone:</strong> {{ $user->phone ?? 'Not provided' }}</p>
                        <p><strong>Member since:</strong> {{ $user->created_at->format('M d, Y') }}</p>
                    </div>
                </div>
                @if($user->address)
                    <div class="row">
                        <div class="col-12">
                            <p><strong>Address:</strong> {{ $user->address }}</p>
                        </div>
                    </div>
                @endif
                <div class="mt-3">
                    <a href="{{ route('profile') }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Profile
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-light">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Account Status
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <p class="mb-0"><strong>Active Account</strong></p>
                    <small class="text-muted">Your account is in good standing</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-primary w-100" disabled>
                            <i class="fas fa-paw"></i> My Pets
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" disabled>
                            <i class="fas fa-heart"></i> Adoptions
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-info w-100" disabled>
                            <i class="fas fa-stethoscope"></i> Vet Bookings
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-warning w-100" disabled>
                            <i class="fas fa-search"></i> Lost Pets
                        </button>
                    </div>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i> 
                        These features will be available soon. Stay tuned!
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock"></i> Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <p class="text-muted mb-0">No recent activity to display.</p>
                    <small class="text-muted">Your activities will appear here once you start using PawPortal features.</small>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
